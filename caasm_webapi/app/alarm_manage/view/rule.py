from multiprocessing import Process

from rest_framework.views import APIView

from caasm_alarm.gen_alarm import gen_alarm
from caasm_service.constants.alarm import (
    ALARM_RULE_SOURCE_OPERATION_MAPPER,
    ALARM_RULE_TEMPLATE_TYPE_MAPPER,
    AlarmRuleTypeEnum,
    ALARM_INSTANCE_RESULT_TYPE_MAPPER,
    AlarmRuleTemplateTypeEnum,
)
from caasm_service.runtime import alarm_rule_instance_service, alarm_rule_run_record_service
from caasm_webapi.app.alarm_manage.serializers.rule import (
    AlarmRuleAddSerializer,
    AlarmRuleListSerializer,
    AlarmRuleInstanceDeleteSerializer,
    AlarmRuleInstanceSerializer,
    AlarmRuleInstanceUpdateSerializer,
    AlarmRuleInstanceEnableSerializer,
    AlarmRuleInstanceStartSerializer,
)
from caasm_webapi.util.response import build_failed, ResponseCode, build_success
from caasm_config.config import caasm_config


class AlarmRuleAddView(APIView):
    def post(self, request):
        """
        新增告警规则
        """
        serializer = AlarmRuleAddSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)
        data = serializer.save()
        rule_template = data.get("rule_template")
        if rule_template == AlarmRuleTemplateTypeEnum.SOURCE_CHECK.value and not data.get("source_fields", []):
            # "not $.vulnerability.malic_files.exists() or not $.vulnerability.weak_passwd.exists() or not $.vulnerability.virus.exists()"

            data["rule_condition_statement_model"] = {
                "model": "asql",
                "statement": " or ".join([f"not $.{item}.exists()" for item in data.get("source_fields", [])]),
            }
        alarm_rule_instance_service.add_alarm_rule_instance(values=alarm_rule_instance_service.load_entity(**data))
        return build_success()


class AlarmRuleListView(APIView):
    def get(self, request):
        serializer = AlarmRuleListSerializer(data=request.query_params)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)

        data = serializer.validated_data

        rule_name = data.get("ruleName")
        rule_category = data.get("ruleCategory")
        rule_level = data.get("ruleLevel")
        page_index = data.get("page_index")
        page_size = data.get("page_size")
        enabled = data.get("enable")
        keyword = data.get("keyword")

        count = alarm_rule_instance_service.get_alarm_rule_instance_count(
            rule_name=rule_name, rule_category=rule_category, enabled=enabled, keyword=keyword
        )
        alarm_rule_info = list(
            alarm_rule_instance_service.find_alarm_rule_instance_info(
                keyword=keyword,
                enabled=enabled,
                rule_level=rule_level,
                rule_name=rule_name,
                rule_category=rule_category,
                page_index=page_index,
                page_size=page_size,
            )
        )
        return build_success(
            data={"count": count, "data": AlarmRuleInstanceSerializer(instance=alarm_rule_info, many=True).data}
        )


class AlarmRuleDeleteView(APIView):
    def post(self, request):
        serializer = AlarmRuleInstanceDeleteSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)
        data = serializer.save()

        rule_instance_ids = data.get("rule_ids")
        delete_ids = []
        for instance_id in rule_instance_ids:
            rule_info = alarm_rule_instance_service.get_alarm_rule_instance_info(rule_id=instance_id)
            if not rule_info:
                continue
            if rule_info.rule_type == AlarmRuleTypeEnum.INTERNAL:
                continue
            delete_ids.append(instance_id)

        alarm_rule_instance_service.delete_alarm_rule_instance_info(rule_ids=delete_ids)

        delete_count = len(delete_ids)
        total_count = len(rule_instance_ids)

        if total_count - delete_count:
            return build_success(data=f"已成功删除{delete_count},未成功删除{total_count - delete_count} 由于内置未成功删除！")
        else:
            return build_success(data=f"已成功删除{delete_count}")


class AlarmRuleUpdateView(APIView):
    def post(self, request):
        serializer = AlarmRuleInstanceUpdateSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)
        data = serializer.save()

        rule_id = data.pop("rule_id")
        rule_template = data.get("rule_template")
        if rule_template == AlarmRuleTemplateTypeEnum.SOURCE_CHECK.value and data.get("source_fields", []):
            # "not $.vulnerability.malic_files.exists() or not $.vulnerability.weak_passwd.exists() or not $.vulnerability.virus.exists()"

            data["rule_condition_statement_model"] = {
                "model": "asql",
                "statement": " or ".join([f"not $.{item}.exists()" for item in data.get("source_fields", [])]),
            }

        alarm_rule_instance_service.update_alarm_rule_instance_by_id(rule_id=rule_id, value=data)
        return build_success()


class AlarmRuleEnableUpdateView(APIView):
    def post(self, request):
        serializer = AlarmRuleInstanceEnableSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)
        data = serializer.save()

        rule_instance_ids = data.get("rule_ids")
        enabled = data.get("enabled")

        alarm_rule_instance_service.update_alarm_rule_instance_by_ids(
            rule_ids=rule_instance_ids, value={"enabled": enabled}
        )

        return build_success()


class AlarmRuleResultTypeView(APIView):
    def get(self, request):
        data = []
        for key, value in ALARM_INSTANCE_RESULT_TYPE_MAPPER.items():
            data.append({"label": value, "value": key})
        return build_success(data=data)


class AlarmRuleChooseView(APIView):
    def get(self, request):
        result = []

        data = alarm_rule_instance_service.find_alarm_rule_instance_info()

        for item in data:
            result.append({"value": str(item.id), "label": item.rule_name})
        return build_success(data=result)


class AlarmRuleInstanceRunView(APIView):
    def post(self, request):
        serializer = AlarmRuleInstanceStartSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)
        data = serializer.save()
        rule_id = data.get("rule_id")

        p = Process(target=gen_alarm, args=(rule_id,))
        p.start()
        return build_success()


class AlarmRuleOperationAPIView(APIView):
    def get(self, request):
        data = []
        for key, value in ALARM_RULE_SOURCE_OPERATION_MAPPER.items():
            data.append({"label": value, "value": key.value})
        return build_success(data=data)


class AlarmRuleTemplateTypeChooseView(APIView):
    def get(self, request):
        data = []
        for key, value in ALARM_RULE_TEMPLATE_TYPE_MAPPER.items():
            data.append({"label": value, "value": key.value})
        return build_success(data=data)


class AlarmRuleAssetTypeListView(APIView):
    def get(self, request):
        data = []
        for item_k, item_v in caasm_config.FETCH_TYPE_MAPPER.items():
            data.append({"label": item_v, "value": item_k})
        return build_success(data=data)
