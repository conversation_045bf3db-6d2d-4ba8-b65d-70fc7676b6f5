from caasm_web_tool.patch.serializer import SerializerMixin
from rest_framework import serializers

from caasm_service.constants.alarm import AlarmDisposalStatus


class AlarmRecordListSerializer(SerializerMixin):

    keyword = serializers.CharField(help_text="关键字", required=False)

    alarm_name = serializers.CharField(help_text="规则名称", required=False)
    ruleId = serializers.CharField(help_text="规则ID", required=False)
    alarm_category = serializers.CharField(help_text="模版分类", required=False)
    alarmLevel = serializers.CharField(help_text="告警等级", required=False)
    page_index = serializers.IntegerField(help_text="页码", required=False, default=1)
    page_size = serializers.IntegerField(help_text="每页最大小", required=False, default=20)
    sort_fields = serializers.ListField(
        child=serializers.Char<PERSON><PERSON>(default="-update_time"),
        help_text="排序字段",
        required=False,
        default=["-update_time"],
    )
    archived = serializers.BooleanField(help_text="是否归档", required=False, default=None, allow_null=True)


class AlarmRecordUpdateSerializer(serializers.Serializer):

    record_id = serializers.CharField(required=True, help_text="告警记录ID")
    disposal_status = serializers.ChoiceField(
        required=False,
        help_text="处置状态",
        choices=[item.value for item in AlarmDisposalStatus],
        default=None,
        allow_null=True,
    )
    archived = serializers.BooleanField(help_text="是否归档", required=False, default=None, allow_null=True)

    def validate(self, attrs):
        disposal_status = attrs.get("disposal_status", None)
        if disposal_status is not None:
            attrs["disposal_status"] = AlarmDisposalStatus(disposal_status)
        return attrs


class AlarmAdapterSourceFieldSerializer(serializers.Serializer):
    adapter_name = serializers.CharField(required=True, help_text="源适配器")
    asset_type = serializers.CharField(required=True, help_text="源数据资产类型")
