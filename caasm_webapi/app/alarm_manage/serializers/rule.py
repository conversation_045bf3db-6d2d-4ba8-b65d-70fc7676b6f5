import datetime
import traceback

from apscheduler.triggers.cron import CronTrigger

from caasm_meta_data.constants import CATEGORY_TRANSLATE
from caasm_service.constants.alarm import (
    AlarmRuleInstanceRunStatus,
    AlarmRuleSourceOperationEnum,
    AlarmRuleTemplateTypeEnum,
    AlarmSeverityLevelEnum,
    AlarmRuleResultTypeEnum,
    ALARM_INSTANCE_RUN_STATUS_MAPPER,
    ALARM_RULE_LEVEL_MAPPER,
    ALARM_RULE_TYPE_MAPPER,
    ALARM_TRIGGER_TYPE,
    ALARM_RESULT_STATEMENT_MODEL_MAPPER,
    AlarmTriggerTypeEnum,
    AlarmRuleTypeEnum,
)
from caasm_service.constants.trigger import TriggerType
from caasm_service.runtime import alarm_rule_instance_service, alarm_rule_run_record_service, job_service
from caasm_tool.constants import DATETIME_FORMAT
from caasm_web_tool.patch.serializer import SerializerMixin
from rest_framework import serializers
import logging

from caasm_tool.re_table import INT_RE

log = logging.getLogger()


class RuleInstanceTriggerShow(SerializerMixin):
    value = serializers.SerializerMethodField("get_value", help_text="类型", required=False)
    type = serializers.SerializerMethodField("get_type", help_text="类型", required=False)

    def get_type(self, obj):
        return obj.type.value

    def get_value(self, obj):
        result = obj.value
        if not result:
            result = {"day": "*/1", "hour": "0", "minute": "0"}
        day = result.get("day", "*/1")
        _temp_time = int("".join(list(filter(str.isdigit, day)))) - 1
        result["day"] = f"*/{_temp_time}"
        return result


class ruleInstanceTriggerAdd(SerializerMixin):
    value = serializers.DictField(help_text="触发器信息")
    type = serializers.SerializerMethodField("get_type", help_text="类型", required=False)

    def get_type(self, obj):
        return obj.type.value


class AlarmRuleSourceDataSerializer(serializers.Serializer):
    source_data_field = serializers.CharField(help_text="源数据字段名", default="", allow_null=True)
    operation = serializers.ChoiceField(
        choices=[item.value for item in AlarmRuleSourceOperationEnum],
        default=AlarmRuleSourceOperationEnum.EQUAL.value,
        help_text="操作符",
        allow_null=True,
    )
    target_data_field = serializers.CharField(help_text="资产台帐的字段名", default="", allow_null=True)


class AlarmRuleAddSerializer(SerializerMixin):
    """
    规则增加处理流程
    """

    rule_name = serializers.CharField(help_text="规则名称", required=True)
    alarm_name = serializers.CharField(help_text="告警名称", required=True)
    alarm_type = serializers.CharField(help_text="告警类型", required=True)
    rule_category = serializers.CharField(help_text="规则分类", required=False)
    rule_description = serializers.CharField(help_text="规则描述", required=False)
    enabled = serializers.BooleanField(help_text="实例状态", required=False, default=False, allow_null=True)
    run_status = serializers.ChoiceField(
        help_text="运行状态", choices=AlarmRuleInstanceRunStatus._value2member_map_, required=False
    )
    rule_level = serializers.ChoiceField(
        help_text="规则等级", choices=AlarmSeverityLevelEnum._value2member_map_, required=True
    )

    result_type = serializers.ChoiceField(
        help_text="规则类型", choices=AlarmRuleResultTypeEnum._value2member_map_, required=True
    )

    rule_scope_statement_model = serializers.DictField(required=False)

    rule_condition_statement_model = serializers.DictField(required=True)

    trigger_type = serializers.CharField()
    trigger = ruleInstanceTriggerAdd(required=False, allow_null=True, default=None)
    rule_template = serializers.ChoiceField(
        choices=[item.value for item in AlarmRuleTemplateTypeEnum], help_text="规则模版类型"
    )
    source_adapter = serializers.CharField(required=False, help_text="源适配器", default="", allow_null=True)
    source_asset_type = serializers.CharField(required=False, help_text="源数据资产类型", default="", allow_null=True)
    target_asset_type = serializers.CharField(required=False, help_text="告警数据资产类型", default="", allow_null=True)
    source_data_config = AlarmRuleSourceDataSerializer(required=False, default=None, allow_null=True)
    source_fields = serializers.ListField(
        child=serializers.CharField(), help_text="源数据字段列表", required=False, default=[]
    )

    def validate(self, attrs):
        rule_name = attrs["rule_name"]
        count = alarm_rule_instance_service.get_alarm_rule_instance_count(rule_name=rule_name)
        if count:
            raise serializers.ValidationError("规则名称已经存在")
        return self._validate_core(attrs)

    def _validate_core(self, attrs):
        trigger = attrs.get("trigger") or {}

        trigger_type = attrs.get("trigger_type")
        if trigger_type not in ["default", "customize", "after_data_collect"]:
            raise serializers.ValidationError("运行模式无效")

        if trigger_type == "default":
            trigger = {"type": "cron", "value": {"hour": "0", "minute": "0", "day": "*/1"}}
        elif trigger_type == "after_data_collect":
            trigger = {"type": "cron", "value": {}}
        else:
            trigger = self._validate_trigger(trigger)
        attrs["trigger"] = trigger
        return attrs

    @classmethod
    def _validate_trigger(cls, trigger):
        if not trigger:
            return {}

        trigger_type = trigger.get("type", TriggerType.CRON.value)
        trigger_value = trigger.get("value") or {}
        trigger_value["day"] = trigger_value.get("day")
        day = trigger_value["day"]
        hour = trigger_value.get("hour", "0") or "0"
        minute = trigger_value.get("minute", "0") or "0"

        if trigger_type not in TriggerType.__members__.values():
            raise serializers.ValidationError("触发器类型无效")

        if not day:
            raise serializers.ValidationError("触发器日期参数无效")

        _temp = int("".join(list(filter(str.isdigit, day))))
        if _temp >= 30:
            raise serializers.ValidationError(f"目前支持最大间隔时间为0-29天")

        day = f"*/{int(_temp) + 1}"
        trigger_value["day"] = day
        if not (INT_RE.match(hour) and 0 <= int(hour) <= 23):
            raise serializers.ValidationError("触发器小时参数无效")

        if not (INT_RE.match(minute) and 0 <= int(minute) <= 59):
            raise serializers.ValidationError("触发器分钟参数无效")

        new_trigger_value = {"hour": hour, "minute": minute, "day": day}

        try:
            CronTrigger(**trigger_value)
        except Exception as e:
            log.debug(traceback.format_exc())
            raise serializers.ValidationError("触发器参数无效")
        trigger["type"] = trigger_type
        trigger["value"] = new_trigger_value
        return trigger


class AlarmRuleStatementModelSerializer(SerializerMixin):
    model = serializers.SerializerMethodField("get_model")
    model_desc = serializers.SerializerMethodField("get_model_desc")
    statement = serializers.CharField(help_text="对应语句")
    template_id = serializers.CharField(help_text="告警规则实例ID", default="", allow_blank=True, allow_null=True)

    def get_model(self, obj):
        return obj.model.value

    def get_model_desc(self, obj):
        model = obj.model
        return ALARM_RESULT_STATEMENT_MODEL_MAPPER.get(model)


class AlarmRuleInstanceSerializer(SerializerMixin):
    """
    规则实例
    """

    rule_id = serializers.CharField(help_text="告警规则实例ID", source="id")
    rule_name = serializers.CharField(help_text="实例名称")
    alarm_name = serializers.CharField(help_text="告警名称")
    alarm_type = serializers.CharField(help_text="告警类型")
    enabled = serializers.BooleanField(help_text="实例状态")
    enabled_desc = serializers.SerializerMethodField("get_enabled_desc")
    rule_description = serializers.CharField(help_text="描述信息")
    rule_type = serializers.SerializerMethodField("get_rule_type")
    rule_type_desc = serializers.SerializerMethodField("get_rule_type_desc")
    last_sync_time = serializers.SerializerMethodField("get_last_sync_time")
    # serializers.DateTimeField(
    #    format=DATETIME_FORMAT, help_text="最近运行时间", allow_null=None, default=None
    # )
    next_sync_time = serializers.SerializerMethodField("get_next_sync_time")
    create_time = serializers.DateTimeField(format=DATETIME_FORMAT, help_text="创建时间")
    run_status_desc = serializers.SerializerMethodField("get_run_status_desc")
    run_status = serializers.SerializerMethodField("get_run_status")
    rule_category = serializers.CharField(help_text="所属类别")
    rule_category_desc = serializers.SerializerMethodField("get_rule_category_desc")
    rule_scope_statement_model = AlarmRuleStatementModelSerializer()

    rule_condition_statement_model = AlarmRuleStatementModelSerializer()

    trigger = RuleInstanceTriggerShow()
    trigger_type = serializers.CharField()

    trigger_type_desc = serializers.SerializerMethodField("get_trigger_type_desc")

    rule_level = serializers.SerializerMethodField("get_rule_level")
    rule_level_desc = serializers.SerializerMethodField("get_rule_level_desc")

    run_times = serializers.SerializerMethodField("get_run_times")

    result_type = serializers.SerializerMethodField("get_result_type")
    update_time = serializers.DateTimeField(format=DATETIME_FORMAT, help_text="更新时间")
    rule_template = serializers.ChoiceField(
        choices=[item.value for item in AlarmRuleTemplateTypeEnum], help_text="规则模版类型"
    )
    source_adapter = serializers.CharField(required=False, help_text="源适配器", default="")
    source_asset_type = serializers.CharField(required=False, help_text="源数据资产类型", default="")
    target_asset_type = serializers.CharField(required=False, help_text="告警数据资产类型", default="")
    source_data_config = AlarmRuleSourceDataSerializer(required=False, default=None)
    source_fields = serializers.ListField(
        child=serializers.CharField(), help_text="源数据字段列表", required=False, default=[]
    )

    def get_result_type(self, obj):
        return obj.result_type.value

    def get_last_sync_time(self, obj):
        rule_instance_id = obj.id
        rule_info = alarm_rule_run_record_service.get_alarm_run_record_info(instance_id=rule_instance_id)
        if rule_info:
            return rule_info.finish_time.strftime(DATETIME_FORMAT)
        else:
            return ""

    def get_next_sync_time(self, obj):
        trigger_type = obj.trigger_type

        next_run_time = datetime.datetime.now()
        if trigger_type == AlarmTriggerTypeEnum.DEFAULT:
            next_run_time = CronTrigger(day="*/0", hour="0", minute="0").get_next_fire_time(
                next_run_time, next_run_time
            )
            return next_run_time
        if trigger_type == AlarmTriggerTypeEnum.AFTER_DATA_COLLECT:
            job_info = job_service.get_job(name="data_collect")
            if not job_info:
                return ""
            trigger_info = job_info.trigger_info
            return trigger_info.get("start_date")
        if trigger_type == AlarmTriggerTypeEnum.CUSTOMIZE:
            trigger_value = obj.trigger.value
            if trigger_value:
                day = trigger_value.get("day")
                minute = trigger_value.get("minute")
                hour = trigger_value.get("hour")
                next_run_time = CronTrigger(day=day, hour=hour, minute=minute).get_next_fire_time(
                    next_run_time, next_run_time
                )
            else:
                next_run_time = CronTrigger(day="*/0", hour="0", minute="0").get_next_fire_time(
                    next_run_time, next_run_time
                )
            return next_run_time.strftime(DATETIME_FORMAT)

    def get_trigger_type_desc(self, obj):
        trigger_type = obj.trigger_type
        return ALARM_TRIGGER_TYPE.get(trigger_type)

    def get_run_times(self, obj):
        rule_id = obj.id
        count = alarm_rule_run_record_service.get_alarm_run_record_count(instance_id=rule_id)
        return count

    def get_rule_type(self, obj):
        return obj.rule_type.value

    def get_rule_type_desc(self, obj):
        rule_type = obj.rule_type
        return ALARM_RULE_TYPE_MAPPER.get(rule_type)

    def get_rule_level(self, obj):
        return obj.rule_level.value

    def get_rule_level_desc(self, obj):
        rule_level = obj.rule_level
        return ALARM_RULE_LEVEL_MAPPER.get(rule_level)

    def get_rule_category_desc(self, obj):
        category = obj.rule_category
        return CATEGORY_TRANSLATE.get(category)

    def get_enabled_desc(self, obj):
        return "启用" if obj.enabled else "禁用"

    def get_run_status_desc(self, obj):
        run_status = obj.run_status
        return ALARM_INSTANCE_RUN_STATUS_MAPPER.get(run_status, "待运行")

    def get_connect_status(self, obj):
        return obj.connect_status.value

    def get_run_status(self, obj):
        return obj.run_status.value

    def get_trigger_desc(self, obj):
        if not obj.enabled:
            return ""
        value = obj.trigger.value
        trigger_desc = "每天0点"
        if value:
            day = value.get("day")
            hour = value.get("hour")
            minute = value.get("minute")

            if "*/" in day:
                _temp_time = int("".join(list(filter(str.isdigit, day)))) - 1
                if _temp_time:
                    trigger_desc = f"每间隔{_temp_time}天的"
            elif "-" in day:
                tmp_days = day.split(",")
                day_desc_list = []
                for tmp_day in tmp_days:
                    first_day, next_day = tmp_day.split("-")
                    day_desc_list.append(f"{first_day}号到{next_day}号")

                trigger_desc = f"每月" + ",".join(day_desc_list) + "的"
            else:
                trigger_desc = f"每月{day}号"

            trigger_desc += f"{hour}时{minute}分"
        return trigger_desc + "开始运行"


class AlarmRuleListSerializer(SerializerMixin):
    """
    规则列表查询引擎
    """

    keyword = serializers.CharField(help_text="关键字", required=False)

    ruleName = serializers.CharField(help_text="规则名称", required=False)
    ruleCategory = serializers.CharField(help_text="规则分类", required=False)
    ruleDescription = serializers.CharField(help_text="规则描述", required=False)
    ruleLevel = serializers.CharField(help_text="规则等级", required=False)
    enable = serializers.BooleanField(help_text="实例状态", required=False, default=None, allow_null=True)
    runStatus = serializers.ChoiceField(
        help_text="字段类型", choices=AlarmRuleInstanceRunStatus._value2member_map_, required=False
    )
    pageIndex = serializers.IntegerField(help_text="页码", required=False, default=1)
    pageSize = serializers.IntegerField(help_text="每页最大小", required=False, default=20)
    sortFields = serializers.ListField(
        child=serializers.CharField(default="-update_time"),
        help_text="排序字段",
        required=False,
        default=["-update_time"],
    )
    rule_template = serializers.ChoiceField(
        choices=[item.value for item in AlarmRuleTemplateTypeEnum],
        help_text="规则模版类型",
        required=False,
        default=AlarmRuleTemplateTypeEnum.NORMAL.value,
    )
    source_adapter = serializers.CharField(required=False, help_text="源适配器", default="")
    source_asset_type = serializers.CharField(required=False, help_text="源数据资产类型", default="")
    target_asset_type = serializers.CharField(required=False, help_text="告警数据资产类型", default="")
    source_data_config = AlarmRuleSourceDataSerializer(required=False, default=None)


class AlarmRuleInstanceDeleteSerializer(SerializerMixin):
    rule_ids = serializers.ListField(child=serializers.CharField(), help_text="模版ids", required=False)


class AlarmRuleInstanceUpdateSerializer(AlarmRuleAddSerializer):
    rule_id = serializers.CharField(help_text="模版ID", required=True)

    def validate(self, attrs):
        rule_name = attrs["rule_name"]
        rule_id = attrs["rule_id"]
        rule_info = alarm_rule_instance_service.get_alarm_rule_instance_info(rule_id=rule_id)
        if rule_info.rule_type == AlarmRuleTypeEnum.INTERNAL:
            raise serializers.ValidationError("内置规则不允许修改！")

        count = alarm_rule_instance_service.get_alarm_rule_instance_count(rule_name=rule_name)
        if count:
            if rule_info.rule_name != rule_name:
                raise serializers.ValidationError("规则名称已经存在")
        return self._validate_core(attrs)


class AlarmRuleInstanceEnableSerializer(SerializerMixin):
    rule_ids = serializers.ListField(
        child=serializers.CharField(allow_null=False, allow_blank=False), help_text="模版ids", required=True
    )
    enabled = serializers.BooleanField(help_text="实例状态", required=True)


class AlarmRuleInstanceStartSerializer(SerializerMixin):
    rule_id = serializers.CharField(help_text="模版ID", required=True)

    def validate(self, attrs):
        rule_id = attrs.get("rule_id")

        rule_info = alarm_rule_instance_service.get_alarm_rule_instance_info(rule_id=rule_id)

        if not rule_info:
            raise serializers.ValidationError("该规则不存在")

        if rule_info.run_status == AlarmRuleInstanceRunStatus.DOING:
            raise serializers.ValidationError("请稍等，该规则正在运行中")

        return attrs
