from caasm_web_tool.patch.router import DefaultRouter
from caasm_webapi.app.alarm_manage.view.alarm import AlarmRecordApiView, AlarmNameChooseApiView
from caasm_webapi.app.alarm_manage.view.alarm import (
    AlarmRecordApiView,
    AlarmNameChooseApiView,
    AlarmRecordUpdateApiView,
    AlarmAdapterSourceFieldAPIView,
)
from caasm_webapi.app.alarm_manage.view.rule import (
    AlarmRuleAddView,
    AlarmRuleListView,
    AlarmRuleDeleteView,
    AlarmRuleOperationAPIView,
    AlarmRuleTemplateTypeChooseView,
    AlarmRuleUpdateView,
    AlarmRuleResultTypeView,
    AlarmRuleEnableUpdateView,
    AlarmRuleChooseView,
    AlarmRuleInstanceRunView,
    AlarmRuleAssetTypeListView,
)
from caasm_webapi.app.alarm_manage.view.rule_run_record import RuleRunRecordApiView
from caasm_webapi.app.alarm_manage.view.rule_template import (
    RuleTemplateAddApiView,
    RuleTemplateListApiView,
    RuleTemplateUpdateApiView,
    RuleTemplateDeleteApiView,
    RuleCategoryChooseView,
    RuleTemplateChooseView,
    RuleLevelChooseView,
)

alarm_manage_router = DefaultRouter()

alarm_manage_router.join_path("rule/category/choose/", RuleCategoryChooseView, name="ruleCategory")
alarm_manage_router.join_path("rule/template/choose/", RuleTemplateChooseView, name="RuleTemplateChooseView")
alarm_manage_router.join_path("rule/level/choose/", RuleLevelChooseView, name="RuleLevelChooseView")
alarm_manage_router.join_path("rule/operation/choose/", AlarmRuleOperationAPIView, name="规则模板的操作选择")
alarm_manage_router.join_path("rule/templateType/choose/", AlarmRuleTemplateTypeChooseView, name="规则模板类型选择")
alarm_manage_router.join_path("rule/fetchAssetTypeList/", AlarmRuleAssetTypeListView, name="规则模板类型选择")

# 模版
alarm_manage_router.join_path("rule/template/add/", RuleTemplateAddApiView, name="alarmRuleTemplateAdd")
alarm_manage_router.join_path("rule/template/list/", RuleTemplateListApiView, name="alarmRuleTemplateList")
alarm_manage_router.join_path("rule/template/update/", RuleTemplateUpdateApiView, name="alarmRuleTemplateUpdate")
alarm_manage_router.join_path("rule/template/delete/", RuleTemplateDeleteApiView, name="alarmRuleTemplateDelete")

# 规则
alarm_manage_router.join_path("rule/instance/add/", AlarmRuleAddView, name="AlarmRuleAddView")
alarm_manage_router.join_path("rule/instance/list/", AlarmRuleListView, name="AlarmRuleListView")
alarm_manage_router.join_path("rule/instance/delete/", AlarmRuleDeleteView, name="AlarmRuleDeleteView")
alarm_manage_router.join_path("rule/instance/update/", AlarmRuleUpdateView, name="AlarmRuleUpdateView")
alarm_manage_router.join_path(
    "rule/instance/resultChoose/",
    AlarmRuleResultTypeView,
    name="AlarmRuleResultTypeView",
)
alarm_manage_router.join_path(
    "rule/instance/updateStatus/",
    AlarmRuleEnableUpdateView,
    name="AlarmRuleEnableUpdateView",
)
alarm_manage_router.join_path("rule/instance/choose/", AlarmRuleChooseView, name="AlarmRuleChooseView")
alarm_manage_router.join_path("rule/instance/run/", AlarmRuleInstanceRunView, name="AlarmRuleInstanceRunView")

# 规则执行历史
alarm_manage_router.join_path("rule/instance/runRecord/", RuleRunRecordApiView, name="RuleRunRecordApiView")

# 告警
alarm_manage_router.join_path("alarm/list/", AlarmRecordApiView, name="AlarmRecordApiView")
alarm_manage_router.join_path(
    "alarm/<category>/nameChoose/",
    AlarmNameChooseApiView,
    name="AlarmNameChooseApiView",
)
alarm_manage_router.join_path("alarm/update/", AlarmRecordUpdateApiView, name="AlarmRecordUpdateApiView")
alarm_manage_router.join_path("alarm/adapterSourceField/", AlarmAdapterSourceFieldAPIView, "适配器源字段")

# 适配器告警
# alarm_manage_router.join_path("alarm/adapter")

default_app_config = "caasm_webapi.app.adapter.appconf.CaasmAlarmConfig"
