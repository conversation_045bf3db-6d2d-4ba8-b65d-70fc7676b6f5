from caasm_tool.constants import StrEnum


class SettingName(StrEnum):
    PUBLIC_KEY = "public_key"
    PRIVATE_KEY = "private_key"
    ADAPTER_DEFAULT_LOGO = "adapter_default_logo"
    ASQL_TOTAL_STATISTICS = "asql_total_statistics"
    ASQL_SUCCESS_STATISTICS = "asql_success_statistics"
    API_SYSTEM_CONFIG = "api_system_config"
    FABRIC_FLAG = "fabric_flag_{}"
    SYSTEM_FABRIC = "system_fabric"
    WHITELIST = "whitelist"
    ACCESS_KEY = "access_key"
    SECRET_KEY = "secret_key"
    API_CALL_RECORD_REMOVE_NUM = "api_call_record_remove_num"
    API_CALL_RECORD_REMOVE_UNIT = "api_call_record_remove_unit"
    SNAPSHOT_REMOVE_DAY = "snapshot_remove_day"
    SNAPSHOT_REMOVE_DISK_USAGE_RATE = "snapshot_remove_disk_usage_rate"
    FETCH_COUNT = "{}_{}_fetch_info"
    WORKFLOW_EXCEEDED_TIME = "workflow_exceeded_time"
    EXPORT_LIMIT = "export_limit"
    SystemCode = "system_code"
    License = "license"
    LINEAGE_GLOBAL_ID = "lineage_global_id"
    cvss_v2 = "cvss_v2"
    cvss_v3 = "cvss_v3"
    intelligence = "intelligence"
    Export_vuln_example = "export_vuln_example"
    ADAPTER_ALARM_RULE = "adapter_alarm_rule"
