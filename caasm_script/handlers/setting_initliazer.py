import os
from Crypto import Random
from Crypto.PublicKey import RSA
import yaml

from caasm_script import SCRIPT_DATA_PATH
from caasm_script.handlers._base import _InitializeHandler
from caasm_service.constants.setting import SettingName
from caasm_service.runtime import setting_service
from caasm_tool.util import get_random_string
from caasm_service.entity.setting import Setting
from caasm_service.schema.setting import SettingSchema
from caasm_config.config import caasm_config
from caasm_persistence.handler.runtime import mongo_handler


class SettingInitializer(_InitializeHandler):
    @classmethod
    def name(cls):
        return "setting"

    @classmethod
    def init_step_1create_snapshot_record_clear_day(cls):
        setting_service.save_setting_by_name(SettingName.SNAPSHOT_REMOVE_DAY, 30)

    @classmethod
    def init_step_2_create_snapshot_record_clean_disk_usage_rate(cls):
        setting_service.save_setting_by_name(SettingName.SNAPSHOT_REMOVE_DISK_USAGE_RATE, 80)

    @classmethod
    def init_step_3_create_api_call_record_remove(cls):
        setting_service.save_setting_by_name(SettingName.API_CALL_RECORD_REMOVE_NUM, 14)
        setting_service.save_setting_by_name(SettingName.API_CALL_RECORD_REMOVE_UNIT, "天")

    @classmethod
    def init_step_4_create_ak_sk(cls):
        setting_service.save_setting_by_name(SettingName.ACCESS_KEY, get_random_string(16))
        setting_service.save_setting_by_name(SettingName.SECRET_KEY, get_random_string(32))

    @classmethod
    def init_step_5_create_whitelist_switch(cls):
        setting_service.save_setting_by_name(SettingName.WHITELIST, False)

    @classmethod
    def init_step_6_create_auth_key(cls):
        private_key_exists = setting_service.get_setting_count(SettingName.PRIVATE_KEY)
        public_key_exists = setting_service.get_setting_count(SettingName.PUBLIC_KEY)

        if private_key_exists and public_key_exists:
            return

        random_generator = Random.new().read
        rsa = RSA.generate(2048, random_generator)

        cls._create_setting(SettingName.PUBLIC_KEY, rsa.publickey().exportKey().decode("utf-8"), "公钥信息")
        cls._create_setting(SettingName.PRIVATE_KEY, rsa.exportKey().decode("utf-8"), "私钥信息")

    @classmethod
    def init_step_7_create_api_system_config(cls):
        api_system_config: SettingSchema = setting_service.get_setting(SettingName.API_SYSTEM_CONFIG)
        if api_system_config is not None and api_system_config.value.get("use_defined", None):
            return
        title = "未岚科技・ASM"
        try:
            with open(f"{SCRIPT_DATA_PATH}/copyright/title.txt") as f:
                title = f.read()
        except Exception:
            pass
        logo_file = f"{SCRIPT_DATA_PATH}/copyright/logo.png"
        if not os.path.exists(logo_file):
            logo_file = f"{SCRIPT_DATA_PATH}/api_default_logo.png"
        logo_id = cls._create_file(logo_file)

        value = {"title": title, "logo_id": str(logo_id)}
        if api_system_config is None:
            cls._create_setting(SettingName.API_SYSTEM_CONFIG, value=value)
        else:
            api_system_config.value.update(value)
            setting_service.update(api_system_config)

    @classmethod
    def init_step_8_create_adapter_default_logo(cls):
        logo_path = SCRIPT_DATA_PATH / "adapter_default_logo.png"
        # 如果原来有则删除
        setting = setting_service.get_setting(SettingName.ADAPTER_DEFAULT_LOGO)
        if setting:
            setting_service.delete_file(setting.value)
            setting_service.delete_setting(SettingName.ADAPTER_DEFAULT_LOGO)

        # 每次都上传新的logo
        file_id = cls._create_file(logo_path)
        cls._create_setting(SettingName.ADAPTER_DEFAULT_LOGO, file_id, "默认logo")

    @classmethod
    def init_step_8_create_system_code(cls):
        system_code_entity: Setting = setting_service.get_setting(SettingName.SystemCode)
        if system_code_entity:
            setting_service.delete_setting(SettingName.SystemCode)
        setting_service.save_setting_by_name(SettingName.SystemCode, get_random_string())

    @classmethod
    def init_step_9_create_cvss(cls):
        file_path = SCRIPT_DATA_PATH / "view" / "vulnerbility"
        cvss_v2: Setting = setting_service.get_setting(SettingName.cvss_v2)
        if cvss_v2:
            setting_service.delete_setting(SettingName.cvss_v2)
        with open(f"{file_path}/CVSS2.yml", "r", encoding="utf-8") as f:
            content = yaml.safe_load(f.read())
            setting_service.save_setting_by_name(SettingName.cvss_v2, content)
        cvss_v3: Setting = setting_service.get_setting(SettingName.cvss_v3)
        if cvss_v3:
            setting_service.delete_setting(SettingName.cvss_v3)
        with open(f"{file_path}/CVSS3.yml", "r", encoding="utf-8") as f:
            content = yaml.safe_load(f.read())
            setting_service.save_setting_by_name(SettingName.cvss_v3, content)
        intelligence: Setting = setting_service.get_setting(SettingName.intelligence)
        if intelligence:
            setting_service.delete_setting(SettingName.intelligence)
        with open(f"{file_path}/intelligence.yml", "r", encoding="utf-8") as f:
            content = yaml.safe_load(f.read())
            setting_service.save_setting_by_name(SettingName.intelligence, content)

    @classmethod
    def init_step_10_create_export_vuln(cls):
        setting_service.delete_setting(SettingName.Export_vuln_example)
        with open(f"{caasm_config.ROOT_DIR}/caasm_vul/example/vuln_example.xlsx", "rb") as f:
            content = f.read()
            file_id = mongo_handler.save_file(content, filename=f.name)
            setting_service.save_setting_by_name(SettingName.Export_vuln_example, file_id)

    @classmethod
    def init_step_11_create_adapter_alarm(cls):
        setting_service.delete_setting(SettingName.ADAPTER_ALARM_RULE)
        with open(f"{caasm_config.ROOT_DIR}/caasm_alarm/rule.yml", "r") as f:
            content = yaml.safe_load(f.read())
            setting_service.save_setting_by_name(SettingName.ADAPTER_ALARM_RULE, content)

    @classmethod
    def _create_setting(cls, name, value, description=""):
        setting = setting_service.load_entity(name=name.value, value=value, description=description)
        return setting_service.save_setting(setting)

    @classmethod
    def _create_file(cls, file_path):
        with open(file_path, "rb") as fd:
            return setting_service.save_file(fd.read())


POINT = SettingInitializer


if __name__ == "__main__":
    SettingInitializer().execute()
